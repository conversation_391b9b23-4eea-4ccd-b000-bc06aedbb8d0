import { Inject, Provide } from '@midwayjs/core';
import { PromotionRecord } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';

@Provide()
export class PromotionRecordService extends BaseService<PromotionRecord> {
  @Inject()
  ctx: Context;

  constructor() {
    super('推广记录');
  }
  getModel = () => {
    return PromotionRecord;
  };

  /**
   * 获取分享统计数据
   * @returns 分享统计信息
   */
  async getStatistics() {
    const now = new Date();

    // 今日开始时间
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 本周开始时间（周一）
    const weekStart = new Date(todayStart);
    const dayOfWeek = weekStart.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    weekStart.setDate(weekStart.getDate() - daysToMonday);

    // 本月开始时间
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const model = this.getModel();

    // 总推广记录数
    const totalRecords = await model.count();

    // 今日推广记录数
    const todayRecords = await model.count({
      where: {
        shareTime: {
          [Op.gte]: todayStart,
        },
      },
    });

    // 本周推广记录数
    const thisWeekRecords = await model.count({
      where: {
        shareTime: {
          [Op.gte]: weekStart,
        },
      },
    });

    // 本月推广记录数
    const thisMonthRecords = await model.count({
      where: {
        shareTime: {
          [Op.gte]: monthStart,
        },
      },
    });

    // 活跃分享者数量（有推广记录的不重复分享者）
    const activeSharers = await model.count({
      distinct: true,
      col: 'sharerUserId',
    });

    // 计算日均推广记录数
    let avgDailyRecords = 0;
    if (totalRecords > 0) {
      // 获取第一条记录的时间
      const firstRecord = await model.findOne({
        order: [['shareTime', 'ASC']],
        attributes: ['shareTime'],
      });

      if (firstRecord) {
        const firstRecordDate = new Date(firstRecord.shareTime);
        const daysDiff = Math.ceil((now.getTime() - firstRecordDate.getTime()) / (1000 * 60 * 60 * 24));
        avgDailyRecords = Math.round((totalRecords / Math.max(daysDiff, 1)) * 100) / 100;
      }
    }

    return {
      totalRecords,
      todayRecords,
      thisWeekRecords,
      thisMonthRecords,
      avgDailyRecords,
      activeSharers,
    };
  }
}
