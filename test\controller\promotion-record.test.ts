import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';

describe('test/controller/promotion-record.test.ts', () => {

  it('should GET /promotion-record/statistics', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/promotion-record/statistics');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('totalRecords');
    expect(result.body).toHaveProperty('todayRecords');
    expect(result.body).toHaveProperty('thisWeekRecords');
    expect(result.body).toHaveProperty('thisMonthRecords');
    expect(result.body).toHaveProperty('avgDailyRecords');
    expect(result.body).toHaveProperty('activeSharers');
    
    // 验证数据类型
    expect(typeof result.body.totalRecords).toBe('number');
    expect(typeof result.body.todayRecords).toBe('number');
    expect(typeof result.body.thisWeekRecords).toBe('number');
    expect(typeof result.body.thisMonthRecords).toBe('number');
    expect(typeof result.body.avgDailyRecords).toBe('number');
    expect(typeof result.body.activeSharers).toBe('number');

    // close app
    await close(app);
  });
});
