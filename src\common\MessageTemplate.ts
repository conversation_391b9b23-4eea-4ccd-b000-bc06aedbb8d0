import { OrderAttributes } from '../entity';

/** 预约通知消息模板 */
export const createOrderMessage = (order: OrderAttributes) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      thing5: {
        value: '您的订单已提交成功',
      },
      thing8: {
        value: order.address,
      },
      name1: {
        value: order.serviceTime?.toLocaleString() || '未知',
      },
      thing4: {
        value: order.sn,
      },
    },
  };
};

/** 接单成功提醒消息模板 */
export const orderConfirmationMessage = (order: OrderAttributes) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      thing3: {
        value: '您的订单已被商家接单',
        color: '#173177',
      },
      thing2: {
        value: order.employee?.name || '未知',
      },
      phone_number6: {
        value: order.employee?.phone || '未知',
      },
      character_string8: {
        value: order.sn,
      },
    },
  };
};
